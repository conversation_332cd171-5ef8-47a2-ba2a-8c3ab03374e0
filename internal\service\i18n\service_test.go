package i18n

import (
	"context"
	"os"
	"path/filepath"
	"testing"
)

func TestNew(t *testing.T) {
	// Create temporary locales directory for testing
	tempDir := t.TempDir()
	originalWd, _ := os.Getwd()
	defer os.Chdir(originalWd)
	
	// Change to temp directory
	os.Chdir(tempDir)
	
	// Create locales directory
	localesDir := "locales"
	err := os.Mkdir(localesDir, 0755)
	if err != nil {
		t.Fatalf("Failed to create locales directory: %v", err)
	}
	
	// Create test translation files
	translations := map[string]string{
		"pt.json": `{"test_key": "Mensagem de teste"}`,
		"en.json": `{"test_key": "Test message"}`,
		"es.json": `{"test_key": "Mensaje de prueba"}`,
	}
	
	for filename, content := range translations {
		filePath := filepath.Join(localesDir, filename)
		err := os.WriteFile(filePath, []byte(content), 0644)
		if err != nil {
			t.Fatalf("Failed to create translation file %s: %v", filename, err)
		}
	}
	
	// Test service creation
	service, err := New()
	if err != nil {
		t.Fatalf("Failed to create i18n service: %v", err)
	}
	
	if service == nil {
		t.Fatal("Service should not be nil")
	}
	
	if service.bundle == nil {
		t.Fatal("Bundle should not be nil")
	}
	
	if len(service.localizers) != 3 {
		t.Fatalf("Expected 3 localizers, got %d", len(service.localizers))
	}
}

func TestDetectLanguageFromHeader(t *testing.T) {
	service := &Service{}
	
	tests := []struct {
		name           string
		acceptLanguage string
		expected       string
	}{
		{
			name:           "Portuguese Brazil",
			acceptLanguage: "pt-BR,pt;q=0.9,en;q=0.8",
			expected:       "pt",
		},
		{
			name:           "English US",
			acceptLanguage: "en-US,en;q=0.9",
			expected:       "en",
		},
		{
			name:           "Spanish",
			acceptLanguage: "es-ES,es;q=0.9",
			expected:       "es",
		},
		{
			name:           "Unsupported language",
			acceptLanguage: "fr-FR,fr;q=0.9",
			expected:       DefaultLanguage,
		},
		{
			name:           "Empty header",
			acceptLanguage: "",
			expected:       DefaultLanguage,
		},
		{
			name:           "Invalid header",
			acceptLanguage: "invalid-header",
			expected:       DefaultLanguage,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.DetectLanguageFromHeader(tt.acceptLanguage)
			if result != tt.expected {
				t.Errorf("Expected %s, got %s", tt.expected, result)
			}
		})
	}
}

func TestIsLanguageSupported(t *testing.T) {
	service := &Service{}
	
	tests := []struct {
		name     string
		language string
		expected bool
	}{
		{"Portuguese", "pt", true},
		{"English", "en", true},
		{"Spanish", "es", true},
		{"French", "fr", false},
		{"German", "de", false},
		{"Empty", "", false},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.IsLanguageSupported(tt.language)
			if result != tt.expected {
				t.Errorf("Expected %t, got %t", tt.expected, result)
			}
		})
	}
}

func TestNormalizeLanguageCode(t *testing.T) {
	service := &Service{}
	
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{"Portuguese Brazil", "pt-BR", "pt"},
		{"Portuguese", "pt", "pt"},
		{"English US", "en-US", "en"},
		{"English", "en", "en"},
		{"Spanish Spain", "es-ES", "es"},
		{"Spanish", "es", "es"},
		{"French", "fr", "pt"}, // Default fallback
		{"Empty", "", "pt"},    // Default fallback
		{"Uppercase", "EN", "en"},
		{"Mixed case", "Pt-Br", "pt"},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.NormalizeLanguageCode(tt.input)
			if result != tt.expected {
				t.Errorf("Expected %s, got %s", tt.expected, result)
			}
		})
	}
}

func TestGetLanguageFromContext(t *testing.T) {
	tests := []struct {
		name     string
		ctx      context.Context
		expected string
	}{
		{
			name:     "Context with language",
			ctx:      context.WithValue(context.Background(), LanguageContextKey, "en"),
			expected: "en",
		},
		{
			name:     "Context without language",
			ctx:      context.Background(),
			expected: DefaultLanguage,
		},
		{
			name:     "Context with wrong type",
			ctx:      context.WithValue(context.Background(), LanguageContextKey, 123),
			expected: DefaultLanguage,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetLanguageFromContext(tt.ctx)
			if result != tt.expected {
				t.Errorf("Expected %s, got %s", tt.expected, result)
			}
		})
	}
}

func TestSetLanguageInContext(t *testing.T) {
	ctx := context.Background()
	language := "es"
	
	newCtx := SetLanguageInContext(ctx, language)
	
	result := GetLanguageFromContext(newCtx)
	if result != language {
		t.Errorf("Expected %s, got %s", language, result)
	}
}

func TestGetSupportedLanguages(t *testing.T) {
	service := &Service{}
	
	languages := service.GetSupportedLanguages()
	
	expected := []string{"pt", "en", "es"}
	if len(languages) != len(expected) {
		t.Fatalf("Expected %d languages, got %d", len(expected), len(languages))
	}
	
	for i, lang := range languages {
		if lang != expected[i] {
			t.Errorf("Expected language %s at index %d, got %s", expected[i], i, lang)
		}
	}
}
