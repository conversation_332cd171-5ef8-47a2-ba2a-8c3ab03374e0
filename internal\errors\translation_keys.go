package errors

// Translation keys for error messages.
//
// These constants map to keys defined in the translation files (locales/*.json).
// Keys are organized by package in alphabetical order (e.g., auth, financialSheet, financialDna, user, etc.).
//
// Key format:
//   Key<Package>Error<SpecificError> = "<package>.error.<specificError>"
//
// Examples:
//   - User not found (from the user package):
//       KeyUserErrorNotFound = "user.error.notFound"
//   - User not found by ID (from the user package):
//       KeyUserErrorNotFoundById = "user.error.notFoundById"

const (
	// AI Assistant
	// AI Context
	// Apple
	// Auth
	// Billing
	// Content
	// Dashboard
	// Dreamboard
	// Financial DNA
	// Financial Sheet
	// Firebase
	// Gamification
	// Google
	// I18n
	// League
	// League Ranking
	// Notification
	// Progression
	// RBAC
	// S3
	// User
	KeyUserErrorConflict                    = "user.error.conflict"
	KeyUserErrorNotFoundById                = "user.error.notFoundById"
	KeyUserErrorNotFoundByEmail             = "user.error.notFoundByEmail"
	KeyUserErrorNotFoundByReferral          = "user.error.notFoundByReferral"
	KeyUserErrorDeletedNotFoundByEmail      = "user.error.deletedNotFoundByEmail"
	KeyUserErrorConflictUpdate              = "user.error.conflictUpdate"
	KeyUserErrorNotFoundForUpdate           = "user.error.notFoundForUpdate"
	KeyUserErrorNotFoundForDeletion         = "user.error.notFoundForDeletion"
	KeyUserErrorCreateFailed                = "user.error.createFailed"
	KeyUserErrorDeletedCreateFailed         = "user.error.deletedCreateFailed"
	KeyUserErrorFindByIdFailed              = "user.error.findByIdFailed"
	KeyUserErrorFindAllFailed               = "user.error.findAllFailed"
	KeyUserErrorDecodeUserFailed            = "user.error.decodeUserFailed"
	KeyUserErrorAdminUsersNotFound          = "user.error.adminUsersNotFound"
	KeyUserErrorDecodeAdminUserFailed       = "user.error.decodeAdminUserFailed"
	KeyUserErrorFindByEmailFailed           = "user.error.findByEmailFailed"
	KeyUserErrorFindByReferralFailed        = "user.error.findByReferralFailed"
	KeyUserErrorFindByReferringUserIdFailed = "user.error.findByReferringUserIdFailed"
	KeyUserErrorCursorError                 = "user.error.cursorError"
	KeyUserErrorFindWithFilterFailed        = "user.error.findWithFilterFailed"
	KeyUserErrorDeletedFindByEmailFailed    = "user.error.deletedFindByEmailFailed"
	KeyUserErrorInvalidId                   = "user.error.invalidId"
	KeyUserErrorUpdateFailed                = "user.error.updateFailed"
	KeyUserErrorDeleteFailed                = "user.error.deleteFailed"
	KeyUserErrorDeletedConflictExists       = "user.error.deletedConflictExists"
	// Vault
)
