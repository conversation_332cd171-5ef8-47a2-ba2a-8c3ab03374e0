package model

import (
	"net/mail"
	"strings"
	"time"
	"unicode"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/pkg"

	"github.com/google/uuid"
	"github.com/imdario/mergo"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"golang.org/x/crypto/bcrypt"
)

// User representa um usuário da plataforma e seus atributos.
type User struct {
	ObjectID         primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID               string             `json:"id,omitempty" bson:"-"`
	Name             string             `json:"name" bson:"name" form:"name"`
	LastName         string             `json:"lastName" bson:"lastName" form:"lastName"`
	Email            string             `json:"email" bson:"email" form:"email"`
	Password         string             `json:"password,omitempty" bson:"password,omitempty" form:"password"`
	PhotoURL         string             `json:"photo" bson:"photo"`
	CreatedAt        time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt        time.Time          `json:"updatedAt" bson:"updatedAt"`
	Onboarding       *Onboarding        `json:"onboarding" bson:"onboarding"`
	ReferralCode     string             `json:"referralCode" bson:"referralCode"`                             // This user's own code to refer others.
	UsedReferralCode string             `json:"usedReferralCode,omitempty" bson:"usedReferralCode,omitempty"` // The code THIS user entered during sign-up (belongs to the referrer).
	ReferringUserID  string             `json:"referringUserId,omitempty" bson:"referringUserId,omitempty"`   // The ID of the user who referred THIS user.
	Classification   string             `json:"classification" bson:"classification"`
	Roles            []string           `json:"roles" bson:"roles"`
	Phone            string             `json:"phone" bson:"phone" form:"phone"`
	RegisterSource   string             `json:"registerSource" bson:"registerSource" form:"registerSource"`
}

// DeletedUser who is deleted from the plataform.
type DeletedUser struct {
	ObjectID     primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID           string             `json:"id,omitempty" bson:"-"`
	User         *User              `json:"user" bson:"user"`
	DeleteReason *DeleteReason      `json:"deleteReason" bson:"deleteReason"`
	DeletedAt    time.Time          `json:"deletedAt" bson:"deletedAt"`
}

type UserCard struct {
	ID                string    `json:"id,omitempty" bson:"-"`
	Name              string    `json:"name" bson:"name"`
	LastName          string    `json:"lastName" bson:"lastName"`
	Email             string    `json:"email" bson:"email"`
	PhotoURL          string    `json:"photo" bson:"photo"`
	Coins             int64     `json:"coins" bson:"coins"`
	AchievementsCount int64     `json:"achievementsCount" bson:"achievementsCount"`
	CurrentSequence   int       `json:"currentSequence" bson:"currentSequence"`
	Achievements      []*string `json:"achievements" bson:"achievements"`
	ReferralCode      string    `json:"referralCode" bson:"referralCode"`                             // This user's own code to refer others.
	UsedReferralCode  string    `json:"usedReferralCode,omitempty" bson:"usedReferralCode,omitempty"` // The code THIS user entered during sign-up (belongs to the referrer).
	ReferringUserID   string    `json:"referringUserId,omitempty" bson:"referringUserId,omitempty"`   // The ID of the user who referred THIS user.
	Wallet            string    `json:"wallet" bson:"wallet"`
}

// UserGroup is deprecated, use Roles field instead
// Kept for backward compatibility during migration
type UserGroup struct {
	Name       string `json:"name" bson:"name"`
	Identifier string `json:"identifier" bson:"identifier"`
}

// hash função hash para a senha.
func hash(password string) ([]byte, error) {
	return bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
}

// beforeSave prepara a senha criptografada para o usuário.
func (u *User) beforeSave() error {
	hashedPassword, err := hash(u.Password)
	if err != nil {
		return err
	}

	u.Password = string(hashedPassword)
	return nil
}

func (u *User) sanitizeRoles() []string {
	if u.Roles == nil {
		return nil
	}

	sanitizedRoles := make([]string, 0)
	for _, role := range u.Roles {
		if role != "admin" {
			sanitizedRoles = append(sanitizedRoles, role)
		}
	}
	return sanitizedRoles
}

func (u *User) Sanitize() *User {
	u.Password = ""
	if !u.ObjectID.IsZero() {
		u.ID = u.ObjectID.Hex()
	}

	// Remove admin role from response
	if !u.IsAdmin() {
		u.Roles = u.sanitizeRoles()
	}

	return u
}

func (u *User) validateRoles() error {
	for _, role := range u.Roles {
		if role == "admin" {
			return errors.New(errors.Model, errors.AdminUnauthorized, errors.Forbidden, nil)
		}
	}
	return nil
}

func (u *User) IsAdmin() bool {
	if len(u.Roles) > 0 {
		for _, role := range u.Roles {
			if role == "admin" {
				return true
			}
		}
	}

	return false
}

// VerifyPassword verifica a senha do usuário.
func (u *User) VerifyPassword(hashedPassword, password string) error {
	if err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password)); err != nil {
		return errors.New(errors.Model, errors.AuthInvalidCredentials, errors.Unauthorized, err)
	}

	return nil
}

// ResetPassword reseta a senha do usuário.
func (u *User) ResetPassword() error {
	u.Password = uuid.New().String()
	if err := u.beforeSave(); err != nil {
		return errors.New(errors.Model, errors.AuthPasswordResetFailed, errors.Internal, err)
	}
	return nil
}

func (u *User) PrepareUpdate(newUser *User) error {
	hasNewPassword := false
	if newUser.Password != "" && newUser.Password != u.Password {
		hasNewPassword = true
	}

	if err := mergo.Merge(u, newUser, mergo.WithOverride); err != nil {
		return errors.New(errors.Model, errors.UserMergeFailed, errors.Internal, err)
	}

	if err := u.validateUpdate(hasNewPassword); err != nil {
		return err
	}

	if hasNewPassword {
		if err := u.beforeSave(); err != nil {
			return errors.New(errors.Model, errors.AuthPasswordProcessFailed, errors.Internal, err)
		}
	}

	return nil
}

func (u *User) validateUpdate(hasNewPassword bool) error {
	if u.Email == "" {
		return errors.New(errors.Model, errors.UserRequiredEmail, errors.Validation, nil)
	}

	if _, err := mail.ParseAddress(u.Email); err != nil {
		return errors.New(errors.Model, errors.UserInvalidEmail, errors.Validation, err)
	}

	if u.ID == "" {
		return errors.New(errors.Model, errors.UserEmptyId, errors.Validation, nil)
	}

	// Validate ID format before attempting conversion.
	if _, err := primitive.ObjectIDFromHex(u.ID); err != nil {
		return errors.New(errors.Model, errors.UserInvalidID, errors.Validation, err)
	}

	u.ObjectID, _ = primitive.ObjectIDFromHex(u.ID) //Error already checked above

	if hasNewPassword {
		if err := validatePassword(u.Password); err != nil {
			return err
		}
	}

	return nil
}

func (u *User) validateCreate() error {
	if u.Email == "" {
		return errors.New(errors.Model, errors.UserRequiredEmail, errors.Validation, nil)
	}

	if _, err := mail.ParseAddress(u.Email); err != nil {
		return errors.New(errors.Model, errors.UserInvalidEmail, errors.Validation, err)
	}

	if u.Name == "" {
		return errors.New(errors.Model, errors.UserRequiredName, errors.Validation, nil)
	}

	if u.Password == "" {
		return errors.New(errors.Model, errors.UserRequiredPassword, errors.Validation, nil)
	}

	if err := validatePassword(u.Password); err != nil {
		return err
	}

	// Prevent setting admin role during creation
	if err := u.validateRoles(); err != nil {
		return err
	}

	return nil
}

func (u *User) validateLogin() error {
	if u.Email == "" {
		return errors.New(errors.Model, errors.UserRequiredEmail, errors.Validation, nil)
	}
	if _, err := mail.ParseAddress(u.Email); err != nil {
		return errors.New(errors.Model, errors.UserInvalidEmail, errors.Validation, err)
	}
	if u.Password == "" {
		return errors.New(errors.Model, errors.UserRequiredPassword, errors.Validation, nil)
	}
	return nil
}

// PrepareCreate prepara o usuário para ser criado ou atualizado nos repositórios.
func (u *User) PrepareCreate() error {
	u.Name = strings.TrimSpace(u.Name)
	u.Email = strings.ToLower(strings.TrimSpace(u.Email))

	u.CreatedAt = time.Now()
	u.UpdatedAt = u.CreatedAt

	u.ReferralCode = pkg.ReferralCodeGeneration()

	if err := u.validateCreate(); err != nil {
		return err
	}

	if err := u.beforeSave(); err != nil {
		return errors.New(errors.Model, errors.AuthPasswordProcessFailed, errors.Internal, err)
	}

	return nil
}

func (u *User) PrepareLogin() error {
	u.Email = strings.ToLower(strings.TrimSpace(u.Email))

	if err := u.validateLogin(); err != nil {
		return err
	}

	return nil
}

// Validate validates fields for update user updates
func (u *User) Validate() error {
	// Basic fields validation
	if u.Name == "" {
		return errors.New(errors.Model, errors.UserRequiredName, errors.Validation, nil)
	}

	if u.Email == "" {
		return errors.New(errors.Model, errors.UserRequiredEmail, errors.Validation, nil)
	}

	if _, err := mail.ParseAddress(u.Email); err != nil {
		return errors.New(errors.Model, errors.UserInvalidEmail, errors.Validation, err)
	}

	// Removed the create at requirement
	// if u.CreatedAt.IsZero() {
	// 	return errors.New(errors.Model, errors.UserRequiredCreatedAt, errors.Validation, nil)
	// }

	// Removed the update at requirement
	// if !u.UpdatedAt.IsZero() {
	// 	return errors.New(errors.Model, errors.UserForbiddenUpdateAt, errors.Validation, nil)
	// }

	// Add Onboarding validation after user onboarding normalization
	// if u.Onboarding == nil || u.Onboarding == (&Onboarding{}) {
	// 	return errors.New(errors.Model, errors.UserRequiredOnboarding, errors.Validation, nil)
	// }

	if u.ReferralCode == "" {
		return errors.New(errors.Model, errors.UserRequiredReferralCode, errors.Validation, nil)
	}

	if u.Roles != nil {
		return errors.New(errors.Model, errors.AdminUnauthorized, errors.Validation, nil)
	}

	if u.Phone == "" {
		return errors.New(errors.Model, errors.UserRequiredPhone, errors.Validation, nil)
	}

	return nil
}

// validatePassword realiza a validação de senha da plataforma
func validatePassword(password string) error {
	var (
		hasMinLen  = false
		hasUpper   = false
		hasLower   = false
		hasNumber  = false
		hasSpecial = false
	)
	if len(password) >= 6 {
		hasMinLen = true
	}
	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsNumber(char):
			hasNumber = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}
	if !(hasMinLen && hasUpper && hasLower && hasNumber && hasSpecial) {
		return errors.New(errors.Model, errors.UserInvalidPassword, errors.Validation, nil)
	}
	return nil
}

func (u *User) PrepareForgotPassword() error {
	u.Email = strings.ToLower(strings.TrimSpace(u.Email))

	if err := u.validateForgotPassword(); err != nil {
		return err
	}

	return nil
}

func (u *User) validateForgotPassword() error {
	if u.Email == "" {
		return errors.New(errors.Model, errors.UserRequiredEmail, errors.Validation, nil)
	}
	if _, err := mail.ParseAddress(u.Email); err != nil {
		return errors.New(errors.Model, errors.UserInvalidEmail, errors.Validation, err)
	}
	return nil
}
