## Instructions
I need to implement internationalized error messages for the progression package repository layer. Deprecate the Message constant strings in `internal/errors/types.go` and replace them with literal strings in the error creation calls throughout the progression repository implementation. Please follow these specific steps:

1. **Analyze the source file**: Examine `internal/repository/progression/mongo.go` and identify all instances where `errors.New()` is called to create progression-facing errors.

2. **Create translation keys**: For each progression-facing error found, create a corresponding translation key in `internal/errors/translation_keys.go` following the dot notation pattern `KeyProgressionError[Specific]` = `"progression.error.[specific]"` (e.g., `KeyProgressionErrorConflict = "progression.error.conflict"`).

3. **Update error creation**: Replace the `errors.New()` calls in the mongo.go file to use the new translation keys instead of hardcoded error messages.

4. **Replace Message constants with literal strings**: In `internal/repository/progression/mongo.go`, find all error creation calls that use Message constants (like `errors.UserConflictExists`, `errors.UserCreateFailed`, etc.) and replace them with their corresponding literal string values.

5. **Use the exact string values**: Replace each Message constant with its exact string value from `internal/errors/types.go`. For example:
   - `errors.UserConflictExists` → `"user already exists"`
   - `errors.UserCreateFailed` → `"failed to create user"`
   - `errors.UserByIDNotFound` → `"user by ID not found"`

4. **Add translations**: Update all three locale files with user-friendly messages:
   - `locales/pt.json` - Portuguese (PT-BR) messages
   - `locales/en.json` - English messages  
   - `locales/es.json` - Spanish messages

**Requirements**:
- All progression-facing error messages must be friendly and appropriate for a gaming/educational app similar to Duolingo
- Use the existing error helper functions (`NewValidationError`, `NewNotFoundError`, etc.) where appropriate
- Follow the established dot notation pattern for translation keys as shown in the user's memories
- Ensure consistency across all three language files
- Internal errors should NOT expose technical details to users but should still have friendly, generic messages like "Oops! Something went wrong on our end. Please try again later."

**Example transformation**:
```go
// Before:
if mongo.IsDuplicateKeyError(err) {
    return "", errors.New(errors.Repository, errors.UserConflictExists, errors.Conflict, err)
}

// After: 
if mongo.IsDuplicateKeyError(err) {
    return "", errors.New(errors.Repository, "user already exists", errors.Conflict, err)
    // And add KeyUserErrorConflict = "user.error.conflict" to translation_keys.go
}
```

**Translation examples**:
- `pt.json`: `{"user.error.conflict": "Este usuário já existe. Tente com um email diferente!"}`
- `en.json`: `{"user.error.conflict": "This user already exists. Try with a different email!"}`
- `es.json`: `{"user.error.conflict": "Este usuario ya existe. ¡Intenta con un email diferente!"}`

If you need additional context about the error handling architecture, refer to the `docs/architecture/errors/` folder.

**Scope**: Update progression repository error handling, create translation keys, add locale files, and deprecate user-related Message constants from types.go.

**Verification**: All user repository errors use literal strings, translation keys exist for each error, locale files contain user-friendly messages, and user Message constants are removed.